{"ast": null, "code": "import ApiConstants from \"../../adapter/ApiConstants\";\nimport api from \"../../libs/api/index\";\nconst Factories = {\n  fetchUserPromotions: () => {\n    return api.get(ApiConstants.FETCH_USER_PROMOTIONS);\n  },\n  applyPromotion: (promotionId, data) => {\n    return api.post(ApiConstants.USE_PROMOTION.replace(\":promotionId\", promotionId), data);\n  }\n};\nexport default Factories;", "map": {"version": 3, "names": ["ApiConstants", "api", "Factories", "fetchUserPromotions", "get", "FETCH_USER_PROMOTIONS", "applyPromotion", "promotionId", "data", "post", "USE_PROMOTION", "replace"], "sources": ["E:/WDP301_UROOM/Customer/src/redux/promotion/factories.js"], "sourcesContent": ["import ApiConstants from \"../../adapter/ApiConstants\";\r\nimport api from \"../../libs/api/index\";\r\n\r\nconst Factories = {\r\n  fetchUserPromotions: () => {\r\n    return api.get(ApiConstants.FETCH_USER_PROMOTIONS);\r\n  },\r\n  applyPromotion: (promotionId, data) => {\r\n    return api.post(ApiConstants.USE_PROMOTION.replace(\":promotionId\", promotionId), data);\r\n  },\r\n};\r\n\r\nexport default Factories;\r\n"], "mappings": "AAAA,OAAOA,YAAY,MAAM,4BAA4B;AACrD,OAAOC,GAAG,MAAM,sBAAsB;AAEtC,MAAMC,SAAS,GAAG;EAChBC,mBAAmB,EAAEA,CAAA,KAAM;IACzB,OAAOF,GAAG,CAACG,GAAG,CAACJ,YAAY,CAACK,qBAAqB,CAAC;EACpD,CAAC;EACDC,cAAc,EAAEA,CAACC,WAAW,EAAEC,IAAI,KAAK;IACrC,OAAOP,GAAG,CAACQ,IAAI,CAACT,YAAY,CAACU,aAAa,CAACC,OAAO,CAAC,cAAc,EAAEJ,WAAW,CAAC,EAAEC,IAAI,CAAC;EACxF;AACF,CAAC;AAED,eAAeN,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}