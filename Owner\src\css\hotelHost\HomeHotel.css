.booking-app {
    min-height: 100vh;
  }
  
  .booking-navbar {
    background-color: #003580 !important;
    padding: 15px 0;
  }
  
  .flag-icon {
    width: 24px;
    height: 16px;
  }
  
  .hero-section1 {
    background-color: #003580;
    color: white;
    padding: 60px 0 100px;
    min-height: 500px;
  }
  
  .hero-content h1 {
    font-size: 42px;
    font-weight: 700;
    margin-bottom: 20px;
    line-height: 1.2;
  }
  
  .highlight-text {
    color: #4fabe0;
    font-size: 42px;
    font-weight: 700;
    margin: 5px 0;
  }
  
  .hero-description {
    font-size: 18px;
    max-width: 550px;
    line-height: 1.5;
  }
  
  .registration-card {
    background-color: white;
    border-radius: 8px;
    border: 4px solid #ffbb00;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  }
  
  .registration-title {
    color: #333;
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 20px;
  }
  
  .benefit-item {
    display: flex;
    margin-bottom: 15px;
    align-items: flex-start;
  }
  
  .check-icon {
    color: #008009;
    font-weight: bold;
    margin-right: 10px;
    font-size: 18px;
  }
  
  .benefit-text {
    color: #333;
    font-size: 14px;
    line-height: 1.4;
  }
  
  .start-button {
    width: 100%;
    padding: 12px;
    font-size: 16px;
    font-weight: 600;
    margin-top: 15px;
    margin-bottom: 20px;
    background-color: #0071c2 !important;
    border: none !important;
  }
  
  .arrow {
    margin-left: 5px;
  }
  
  .continue-section {
    text-align: center;
  }
  
  .continue-text {
    color: #333;
    font-size: 14px;
    margin-bottom: 5px;
  }
  
  .continue-link {
    color: #0071c2;
    text-decoration: none;
    font-size: 14px;
  }
  
  .continue-link:hover {
    text-decoration: underline;
  }
  
  