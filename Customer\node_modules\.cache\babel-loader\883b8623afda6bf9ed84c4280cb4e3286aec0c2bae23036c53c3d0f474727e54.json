{"ast": null, "code": "import ApiConstants from \"../../adapter/ApiConstants\";\nimport api from \"../../libs/api/index\";\nconst Factories = {\n  fetchUserPromotions: userId => {\n    return api.get(ApiConstants.FETCH_USER_PROMOTIONS.replace(\":userId\", userId));\n  },\n  applyPromotion: (promotionId, data) => {\n    return api.post(ApiConstants.USE_PROMOTION.replace(\":promotionId\", promotionId), data);\n  }\n};\nexport default Factories;", "map": {"version": 3, "names": ["ApiConstants", "api", "Factories", "fetchUserPromotions", "userId", "get", "FETCH_USER_PROMOTIONS", "replace", "applyPromotion", "promotionId", "data", "post", "USE_PROMOTION"], "sources": ["E:/WDP301_UROOM/Customer/src/redux/promotion/factories.js"], "sourcesContent": ["import ApiConstants from \"../../adapter/ApiConstants\";\r\nimport api from \"../../libs/api/index\";\r\n\r\nconst Factories = {\r\n  fetchUserPromotions: (userId) => {\r\n    return api.get(ApiConstants.FETCH_USER_PROMOTIONS.replace(\":userId\", userId));\r\n  },\r\n  applyPromotion: (promotionId, data) => {\r\n    return api.post(ApiConstants.USE_PROMOTION.replace(\":promotionId\", promotionId), data);\r\n  },\r\n};\r\n\r\nexport default Factories;\r\n"], "mappings": "AAAA,OAAOA,YAAY,MAAM,4BAA4B;AACrD,OAAOC,GAAG,MAAM,sBAAsB;AAEtC,MAAMC,SAAS,GAAG;EAChBC,mBAAmB,EAAGC,MAAM,IAAK;IAC/B,OAAOH,GAAG,CAACI,GAAG,CAACL,YAAY,CAACM,qBAAqB,CAACC,OAAO,CAAC,SAAS,EAAEH,MAAM,CAAC,CAAC;EAC/E,CAAC;EACDI,cAAc,EAAEA,CAACC,WAAW,EAAEC,IAAI,KAAK;IACrC,OAAOT,GAAG,CAACU,IAAI,CAACX,YAAY,CAACY,aAAa,CAACL,OAAO,CAAC,cAAc,EAAEE,WAAW,CAAC,EAAEC,IAAI,CAAC;EACxF;AACF,CAAC;AAED,eAAeR,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}