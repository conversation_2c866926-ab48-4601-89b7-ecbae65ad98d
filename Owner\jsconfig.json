{"compilerOptions": {"target": "ES6", "lib": ["DOM", "DOM.Iterable", "ESNext"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "module": "ESNext", "moduleResolution": "Node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "baseUrl": ".", "paths": {"@adapter/*": ["src/adapter/*"], "@components/*": ["src/components/*"], "@css/*": ["src/css/*"], "@images/*": ["src/images/*"], "@libs/*": ["src/libs/*"], "@pages/*": ["src/pages/*"], "@redux/*": ["src/redux/*"], "@utils/*": ["src/utils/*"]}}, "include": ["src"], "exclude": ["node_modules"]}