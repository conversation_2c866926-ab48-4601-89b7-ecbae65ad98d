{"ast": null, "code": "import PromotionActions from \"./actions\";\nconst initialState = {\n  promotions: [],\n  loading: false,\n  error: null\n};\nconst promotionReducer = (state = initialState, action) => {\n  switch (action.type) {\n    case PromotionActions.FETCH_USER_PROMOTIONS:\n      return {\n        ...state,\n        loading: true,\n        error: null\n      };\n    case PromotionActions.FETCH_USER_PROMOTIONS_SUCCESS:\n      return {\n        ...state,\n        loading: false,\n        promotions: action.payload,\n        error: null\n      };\n    case PromotionActions.FETCH_USER_PROMOTIONS_FAILURE:\n      return {\n        ...state,\n        loading: false,\n        error: action.payload\n      };\n    case PromotionActions.USE_PROMOTION_SUCCESS:\n      return {\n        ...state,\n        promotions: state.promotions.map(promotion => promotion._id === action.payload._id ? {\n          ...promotion,\n          isUsed: true,\n          usedAt: new Date().toISOString()\n        } : promotion)\n      };\n    default:\n      return state;\n  }\n};\nexport default promotionReducer;", "map": {"version": 3, "names": ["PromotionActions", "initialState", "promotions", "loading", "error", "promotionReducer", "state", "action", "type", "FETCH_USER_PROMOTIONS", "FETCH_USER_PROMOTIONS_SUCCESS", "payload", "FETCH_USER_PROMOTIONS_FAILURE", "USE_PROMOTION_SUCCESS", "map", "promotion", "_id", "isUsed", "usedAt", "Date", "toISOString"], "sources": ["E:/WDP301_UROOM/Customer/src/redux/promotion/reducer.js"], "sourcesContent": ["import PromotionActions from \"./actions\";\r\n\r\nconst initialState = {\r\n  promotions: [],\r\n  loading: false,\r\n  error: null,\r\n};\r\n\r\nconst promotionReducer = (state = initialState, action) => {\r\n  switch (action.type) {\r\n    case PromotionActions.FETCH_USER_PROMOTIONS:\r\n      return {\r\n        ...state,\r\n        loading: true,\r\n        error: null,\r\n      };\r\n\r\n    case PromotionActions.FETCH_USER_PROMOTIONS_SUCCESS:\r\n      return {\r\n        ...state,\r\n        loading: false,\r\n        promotions: action.payload,\r\n        error: null,\r\n      };\r\n\r\n    case PromotionActions.FETCH_USER_PROMOTIONS_FAILURE:\r\n      return {\r\n        ...state,\r\n        loading: false,\r\n        error: action.payload,\r\n      };\r\n\r\n    case PromotionActions.USE_PROMOTION_SUCCESS:\r\n      return {\r\n        ...state,\r\n        promotions: state.promotions.map((promotion) =>\r\n          promotion._id === action.payload._id \r\n            ? { ...promotion, isUsed: true, usedAt: new Date().toISOString() }\r\n            : promotion\r\n        ),\r\n      };\r\n\r\n    default:\r\n      return state;\r\n  }\r\n};\r\n\r\nexport default promotionReducer;\r\n"], "mappings": "AAAA,OAAOA,gBAAgB,MAAM,WAAW;AAExC,MAAMC,YAAY,GAAG;EACnBC,UAAU,EAAE,EAAE;EACdC,OAAO,EAAE,KAAK;EACdC,KAAK,EAAE;AACT,CAAC;AAED,MAAMC,gBAAgB,GAAGA,CAACC,KAAK,GAAGL,YAAY,EAAEM,MAAM,KAAK;EACzD,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKR,gBAAgB,CAACS,qBAAqB;MACzC,OAAO;QACL,GAAGH,KAAK;QACRH,OAAO,EAAE,IAAI;QACbC,KAAK,EAAE;MACT,CAAC;IAEH,KAAKJ,gBAAgB,CAACU,6BAA6B;MACjD,OAAO;QACL,GAAGJ,KAAK;QACRH,OAAO,EAAE,KAAK;QACdD,UAAU,EAAEK,MAAM,CAACI,OAAO;QAC1BP,KAAK,EAAE;MACT,CAAC;IAEH,KAAKJ,gBAAgB,CAACY,6BAA6B;MACjD,OAAO;QACL,GAAGN,KAAK;QACRH,OAAO,EAAE,KAAK;QACdC,KAAK,EAAEG,MAAM,CAACI;MAChB,CAAC;IAEH,KAAKX,gBAAgB,CAACa,qBAAqB;MACzC,OAAO;QACL,GAAGP,KAAK;QACRJ,UAAU,EAAEI,KAAK,CAACJ,UAAU,CAACY,GAAG,CAAEC,SAAS,IACzCA,SAAS,CAACC,GAAG,KAAKT,MAAM,CAACI,OAAO,CAACK,GAAG,GAChC;UAAE,GAAGD,SAAS;UAAEE,MAAM,EAAE,IAAI;UAAEC,MAAM,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;QAAE,CAAC,GAChEL,SACN;MACF,CAAC;IAEH;MACE,OAAOT,KAAK;EAChB;AACF,CAAC;AAED,eAAeD,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}