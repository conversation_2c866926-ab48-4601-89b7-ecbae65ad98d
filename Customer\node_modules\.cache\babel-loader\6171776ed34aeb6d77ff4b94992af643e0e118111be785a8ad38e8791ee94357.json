{"ast": null, "code": "import { all, call, fork, put, takeEvery } from \"@redux-saga/core/effects\";\nimport PromotionActions from \"./actions\";\nimport Factories from \"./factories\";\n\n// 1. L<PERSON>y danh sách promotion của người dùng\nfunction* getUserPromotions() {\n  yield takeEvery(PromotionActions.FETCH_USER_PROMOTIONS, function* (action) {\n    const {\n      userId,\n      onSuccess,\n      onFailed,\n      onError\n    } = action.payload;\n    try {\n      var _response$data;\n      const response = yield call(() => Factories.fetchUserPromotions(userId));\n      if ((response === null || response === void 0 ? void 0 : response.status) === 200 && (response === null || response === void 0 ? void 0 : (_response$data = response.data) === null || _response$data === void 0 ? void 0 : _response$data.error) === false) {\n        var _response$data2;\n        const promotions = (_response$data2 = response.data) === null || _response$data2 === void 0 ? void 0 : _response$data2.data;\n        yield put({\n          type: PromotionActions.FETCH_USER_PROMOTIONS_SUCCESS,\n          payload: promotions\n        });\n        onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess(promotions);\n      } else {\n        var _response$data3;\n        const message = (response === null || response === void 0 ? void 0 : (_response$data3 = response.data) === null || _response$data3 === void 0 ? void 0 : _response$data3.message) || \"Không lấy được danh sách khuyến mãi\";\n        yield put({\n          type: PromotionActions.FETCH_USER_PROMOTIONS_FAILURE,\n          payload: message\n        });\n        onFailed === null || onFailed === void 0 ? void 0 : onFailed(message);\n      }\n    } catch (error) {\n      var _error$response, _error$response2, _error$response2$data;\n      const status = (_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status;\n      const msg = ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.message) || \"Lỗi server\";\n      yield put({\n        type: PromotionActions.FETCH_USER_PROMOTIONS_FAILURE,\n        payload: msg\n      });\n      if (status >= 500) {\n        onError === null || onError === void 0 ? void 0 : onError(error);\n      } else {\n        onFailed === null || onFailed === void 0 ? void 0 : onFailed(msg);\n      }\n    }\n  });\n}\n\n// 2. Sử dụng promotion\nfunction* usePromotion() {\n  yield takeEvery(PromotionActions.USE_PROMOTION, function* (action) {\n    const {\n      promotionId,\n      data,\n      onSuccess,\n      onFailed,\n      onError\n    } = action.payload;\n    try {\n      var _s = $RefreshSig$(),\n        _response$data4;\n      const response = yield _s(call(_s(() => {\n        _s();\n        return Factories.usePromotion(promotionId, data);\n      }, \"3azvO2XP1Z960/Z3bv5DYInOY6M=\", false, function () {\n        return [Factories.usePromotion];\n      })), \"3azvO2XP1Z960/Z3bv5DYInOY6M=\", false, function () {\n        return [Factories.usePromotion];\n      });\n      if ((response === null || response === void 0 ? void 0 : response.status) === 200 && (response === null || response === void 0 ? void 0 : (_response$data4 = response.data) === null || _response$data4 === void 0 ? void 0 : _response$data4.error) === false) {\n        var _response$data5;\n        const updatedPromotion = (_response$data5 = response.data) === null || _response$data5 === void 0 ? void 0 : _response$data5.data;\n        yield put({\n          type: PromotionActions.USE_PROMOTION_SUCCESS,\n          payload: updatedPromotion\n        });\n        onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess(updatedPromotion);\n      } else {\n        var _response$data6;\n        const message = (response === null || response === void 0 ? void 0 : (_response$data6 = response.data) === null || _response$data6 === void 0 ? void 0 : _response$data6.message) || \"Không thể sử dụng khuyến mãi\";\n        yield put({\n          type: PromotionActions.USE_PROMOTION_FAILURE,\n          payload: message\n        });\n        onFailed === null || onFailed === void 0 ? void 0 : onFailed(message);\n      }\n    } catch (error) {\n      var _error$response3, _error$response4, _error$response4$data;\n      const status = (_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : _error$response3.status;\n      const msg = ((_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : (_error$response4$data = _error$response4.data) === null || _error$response4$data === void 0 ? void 0 : _error$response4$data.message) || \"Lỗi server\";\n      yield put({\n        type: PromotionActions.USE_PROMOTION_FAILURE,\n        payload: msg\n      });\n      if (status >= 500) {\n        onError === null || onError === void 0 ? void 0 : onError(error);\n      } else {\n        onFailed === null || onFailed === void 0 ? void 0 : onFailed(msg);\n      }\n    }\n  });\n}\nexport default function* promotionSaga() {\n  yield all([fork(getUserPromotions), fork(usePromotion)]);\n}", "map": {"version": 3, "names": ["all", "call", "fork", "put", "takeEvery", "PromotionActions", "Factories", "getUserPromotions", "FETCH_USER_PROMOTIONS", "action", "userId", "onSuccess", "onFailed", "onError", "payload", "_response$data", "response", "fetchUserPromotions", "status", "data", "error", "_response$data2", "promotions", "type", "FETCH_USER_PROMOTIONS_SUCCESS", "_response$data3", "message", "FETCH_USER_PROMOTIONS_FAILURE", "_error$response", "_error$response2", "_error$response2$data", "msg", "usePromotion", "USE_PROMOTION", "promotionId", "_s", "$RefreshSig$", "_response$data4", "_response$data5", "updatedPromotion", "USE_PROMOTION_SUCCESS", "_response$data6", "USE_PROMOTION_FAILURE", "_error$response3", "_error$response4", "_error$response4$data", "promotionSaga"], "sources": ["E:/WDP301_UROOM/Customer/src/redux/promotion/saga.js"], "sourcesContent": ["import { all, call, fork, put, takeEvery } from \"@redux-saga/core/effects\";\r\nimport PromotionActions from \"./actions\";\r\nimport Factories from \"./factories\";\r\n\r\n// 1. L<PERSON>y danh sách promotion của người dùng\r\nfunction* getUserPromotions() {\r\n  yield takeEvery(PromotionActions.FETCH_USER_PROMOTIONS, function* (action) {\r\n    const { userId, onSuccess, onFailed, onError } = action.payload;\r\n\r\n    try {\r\n      const response = yield call(() => Factories.fetchUserPromotions(userId));\r\n\r\n      if (response?.status === 200 && response?.data?.error === false) {\r\n        const promotions = response.data?.data;\r\n        yield put({\r\n          type: PromotionActions.FETCH_USER_PROMOTIONS_SUCCESS,\r\n          payload: promotions,\r\n        });\r\n        onSuccess?.(promotions);\r\n      } else {\r\n        const message = response?.data?.message || \"Không lấy được danh sách khuyến mãi\";\r\n        yield put({\r\n          type: PromotionActions.FETCH_USER_PROMOTIONS_FAILURE,\r\n          payload: message,\r\n        });\r\n        onFailed?.(message);\r\n      }\r\n    } catch (error) {\r\n      const status = error.response?.status;\r\n      const msg = error.response?.data?.message || \"Lỗi server\";\r\n      \r\n      yield put({\r\n        type: PromotionActions.FETCH_USER_PROMOTIONS_FAILURE,\r\n        payload: msg,\r\n      });\r\n\r\n      if (status >= 500) {\r\n        onError?.(error);\r\n      } else {\r\n        onFailed?.(msg);\r\n      }\r\n    }\r\n  });\r\n}\r\n\r\n// 2. Sử dụng promotion\r\nfunction* usePromotion() {\r\n  yield takeEvery(PromotionActions.USE_PROMOTION, function* (action) {\r\n    const { promotionId, data, onSuccess, onFailed, onError } = action.payload;\r\n\r\n    try {\r\n      const response = yield call(() => Factories.usePromotion(promotionId, data));\r\n\r\n      if (response?.status === 200 && response?.data?.error === false) {\r\n        const updatedPromotion = response.data?.data;\r\n        yield put({\r\n          type: PromotionActions.USE_PROMOTION_SUCCESS,\r\n          payload: updatedPromotion,\r\n        });\r\n        onSuccess?.(updatedPromotion);\r\n      } else {\r\n        const message = response?.data?.message || \"Không thể sử dụng khuyến mãi\";\r\n        yield put({\r\n          type: PromotionActions.USE_PROMOTION_FAILURE,\r\n          payload: message,\r\n        });\r\n        onFailed?.(message);\r\n      }\r\n    } catch (error) {\r\n      const status = error.response?.status;\r\n      const msg = error.response?.data?.message || \"Lỗi server\";\r\n      \r\n      yield put({\r\n        type: PromotionActions.USE_PROMOTION_FAILURE,\r\n        payload: msg,\r\n      });\r\n\r\n      if (status >= 500) {\r\n        onError?.(error);\r\n      } else {\r\n        onFailed?.(msg);\r\n      }\r\n    }\r\n  });\r\n}\r\n\r\nexport default function* promotionSaga() {\r\n  yield all([\r\n    fork(getUserPromotions),\r\n    fork(usePromotion),\r\n  ]);\r\n}\r\n"], "mappings": "AAAA,SAASA,GAAG,EAAEC,IAAI,EAAEC,IAAI,EAAEC,GAAG,EAAEC,SAAS,QAAQ,0BAA0B;AAC1E,OAAOC,gBAAgB,MAAM,WAAW;AACxC,OAAOC,SAAS,MAAM,aAAa;;AAEnC;AACA,UAAUC,iBAAiBA,CAAA,EAAG;EAC5B,MAAMH,SAAS,CAACC,gBAAgB,CAACG,qBAAqB,EAAE,WAAWC,MAAM,EAAE;IACzE,MAAM;MAAEC,MAAM;MAAEC,SAAS;MAAEC,QAAQ;MAAEC;IAAQ,CAAC,GAAGJ,MAAM,CAACK,OAAO;IAE/D,IAAI;MAAA,IAAAC,cAAA;MACF,MAAMC,QAAQ,GAAG,MAAMf,IAAI,CAAC,MAAMK,SAAS,CAACW,mBAAmB,CAACP,MAAM,CAAC,CAAC;MAExE,IAAI,CAAAM,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEE,MAAM,MAAK,GAAG,IAAI,CAAAF,QAAQ,aAARA,QAAQ,wBAAAD,cAAA,GAARC,QAAQ,CAAEG,IAAI,cAAAJ,cAAA,uBAAdA,cAAA,CAAgBK,KAAK,MAAK,KAAK,EAAE;QAAA,IAAAC,eAAA;QAC/D,MAAMC,UAAU,IAAAD,eAAA,GAAGL,QAAQ,CAACG,IAAI,cAAAE,eAAA,uBAAbA,eAAA,CAAeF,IAAI;QACtC,MAAMhB,GAAG,CAAC;UACRoB,IAAI,EAAElB,gBAAgB,CAACmB,6BAA6B;UACpDV,OAAO,EAAEQ;QACX,CAAC,CAAC;QACFX,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAGW,UAAU,CAAC;MACzB,CAAC,MAAM;QAAA,IAAAG,eAAA;QACL,MAAMC,OAAO,GAAG,CAAAV,QAAQ,aAARA,QAAQ,wBAAAS,eAAA,GAART,QAAQ,CAAEG,IAAI,cAAAM,eAAA,uBAAdA,eAAA,CAAgBC,OAAO,KAAI,qCAAqC;QAChF,MAAMvB,GAAG,CAAC;UACRoB,IAAI,EAAElB,gBAAgB,CAACsB,6BAA6B;UACpDb,OAAO,EAAEY;QACX,CAAC,CAAC;QACFd,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAGc,OAAO,CAAC;MACrB;IACF,CAAC,CAAC,OAAON,KAAK,EAAE;MAAA,IAAAQ,eAAA,EAAAC,gBAAA,EAAAC,qBAAA;MACd,MAAMZ,MAAM,IAAAU,eAAA,GAAGR,KAAK,CAACJ,QAAQ,cAAAY,eAAA,uBAAdA,eAAA,CAAgBV,MAAM;MACrC,MAAMa,GAAG,GAAG,EAAAF,gBAAA,GAAAT,KAAK,CAACJ,QAAQ,cAAAa,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBV,IAAI,cAAAW,qBAAA,uBAApBA,qBAAA,CAAsBJ,OAAO,KAAI,YAAY;MAEzD,MAAMvB,GAAG,CAAC;QACRoB,IAAI,EAAElB,gBAAgB,CAACsB,6BAA6B;QACpDb,OAAO,EAAEiB;MACX,CAAC,CAAC;MAEF,IAAIb,MAAM,IAAI,GAAG,EAAE;QACjBL,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAGO,KAAK,CAAC;MAClB,CAAC,MAAM;QACLR,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAGmB,GAAG,CAAC;MACjB;IACF;EACF,CAAC,CAAC;AACJ;;AAEA;AACA,UAAUC,YAAYA,CAAA,EAAG;EACvB,MAAM5B,SAAS,CAACC,gBAAgB,CAAC4B,aAAa,EAAE,WAAWxB,MAAM,EAAE;IACjE,MAAM;MAAEyB,WAAW;MAAEf,IAAI;MAAER,SAAS;MAAEC,QAAQ;MAAEC;IAAQ,CAAC,GAAGJ,MAAM,CAACK,OAAO;IAE1E,IAAI;MAAA,IAAAqB,EAAA,GAAAC,YAAA;QAAAC,eAAA;MACF,MAAMrB,QAAQ,GAAG,MAAAmB,EAAA,CAAMlC,IAAI,CAAAkC,EAAA,CAAC;QAAAA,EAAA;QAAA,OAAM7B,SAAS,CAAC0B,YAAY,CAACE,WAAW,EAAEf,IAAI,CAAC;MAAA;QAAA,QAAzCb,SAAS,CAAC0B,YAAY;MAAA,EAAmB,CAAC;QAAA,QAA1C1B,SAAS,CAAC0B,YAAY;MAAA,EAAoB;MAE5E,IAAI,CAAAhB,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEE,MAAM,MAAK,GAAG,IAAI,CAAAF,QAAQ,aAARA,QAAQ,wBAAAqB,eAAA,GAARrB,QAAQ,CAAEG,IAAI,cAAAkB,eAAA,uBAAdA,eAAA,CAAgBjB,KAAK,MAAK,KAAK,EAAE;QAAA,IAAAkB,eAAA;QAC/D,MAAMC,gBAAgB,IAAAD,eAAA,GAAGtB,QAAQ,CAACG,IAAI,cAAAmB,eAAA,uBAAbA,eAAA,CAAenB,IAAI;QAC5C,MAAMhB,GAAG,CAAC;UACRoB,IAAI,EAAElB,gBAAgB,CAACmC,qBAAqB;UAC5C1B,OAAO,EAAEyB;QACX,CAAC,CAAC;QACF5B,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAG4B,gBAAgB,CAAC;MAC/B,CAAC,MAAM;QAAA,IAAAE,eAAA;QACL,MAAMf,OAAO,GAAG,CAAAV,QAAQ,aAARA,QAAQ,wBAAAyB,eAAA,GAARzB,QAAQ,CAAEG,IAAI,cAAAsB,eAAA,uBAAdA,eAAA,CAAgBf,OAAO,KAAI,8BAA8B;QACzE,MAAMvB,GAAG,CAAC;UACRoB,IAAI,EAAElB,gBAAgB,CAACqC,qBAAqB;UAC5C5B,OAAO,EAAEY;QACX,CAAC,CAAC;QACFd,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAGc,OAAO,CAAC;MACrB;IACF,CAAC,CAAC,OAAON,KAAK,EAAE;MAAA,IAAAuB,gBAAA,EAAAC,gBAAA,EAAAC,qBAAA;MACd,MAAM3B,MAAM,IAAAyB,gBAAA,GAAGvB,KAAK,CAACJ,QAAQ,cAAA2B,gBAAA,uBAAdA,gBAAA,CAAgBzB,MAAM;MACrC,MAAMa,GAAG,GAAG,EAAAa,gBAAA,GAAAxB,KAAK,CAACJ,QAAQ,cAAA4B,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBzB,IAAI,cAAA0B,qBAAA,uBAApBA,qBAAA,CAAsBnB,OAAO,KAAI,YAAY;MAEzD,MAAMvB,GAAG,CAAC;QACRoB,IAAI,EAAElB,gBAAgB,CAACqC,qBAAqB;QAC5C5B,OAAO,EAAEiB;MACX,CAAC,CAAC;MAEF,IAAIb,MAAM,IAAI,GAAG,EAAE;QACjBL,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAGO,KAAK,CAAC;MAClB,CAAC,MAAM;QACLR,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAGmB,GAAG,CAAC;MACjB;IACF;EACF,CAAC,CAAC;AACJ;AAEA,eAAe,UAAUe,aAAaA,CAAA,EAAG;EACvC,MAAM9C,GAAG,CAAC,CACRE,IAAI,CAACK,iBAAiB,CAAC,EACvBL,IAAI,CAAC8B,YAAY,CAAC,CACnB,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}