{"ast": null, "code": "import ApiConstants from \"../../adapter/ApiConstants\";\nimport api from \"../../libs/api/index\";\nconst Factories = {\n  fetchUserPromotions: () => {\n    return api.get(ApiConstants.FETCH_USER_PROMOTIONS);\n  },\n  applyPromotion: data => {\n    return api.post(ApiConstants.USE_PROMOTION, data);\n  }\n};\nexport default Factories;", "map": {"version": 3, "names": ["ApiConstants", "api", "Factories", "fetchUserPromotions", "get", "FETCH_USER_PROMOTIONS", "applyPromotion", "data", "post", "USE_PROMOTION"], "sources": ["E:/WDP301_UROOM/Customer/src/redux/promotion/factories.js"], "sourcesContent": ["import ApiConstants from \"../../adapter/ApiConstants\";\r\nimport api from \"../../libs/api/index\";\r\n\r\nconst Factories = {\r\n  fetchUserPromotions: () => {\r\n    return api.get(ApiConstants.FETCH_USER_PROMOTIONS);\r\n  },\r\n  applyPromotion: (data) => {\r\n    return api.post(ApiConstants.USE_PROMOTION, data);\r\n  },\r\n};\r\n\r\nexport default Factories;\r\n"], "mappings": "AAAA,OAAOA,YAAY,MAAM,4BAA4B;AACrD,OAAOC,GAAG,MAAM,sBAAsB;AAEtC,MAAMC,SAAS,GAAG;EAChBC,mBAAmB,EAAEA,CAAA,KAAM;IACzB,OAAOF,GAAG,CAACG,GAAG,CAACJ,YAAY,CAACK,qBAAqB,CAAC;EACpD,CAAC;EACDC,cAAc,EAAGC,IAAI,IAAK;IACxB,OAAON,GAAG,CAACO,IAAI,CAACR,YAAY,CAACS,aAAa,EAAEF,IAAI,CAAC;EACnD;AACF,CAAC;AAED,eAAeL,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}