{"ast": null, "code": "import { all } from 'redux-saga/effects';\nimport AuthSaga from './auth/saga';\nimport SearchSaga from './search/saga';\nimport HotelSaga from './hotel/saga';\nimport RoomSaga from './room/saga';\nimport FeedbackSaga from './feedback/saga';\nimport ReservationSaga from './reservations/saga';\nimport ReportFeedbackSaga from './reportedFeedback/saga';\nimport MessageSaga from './message/saga';\nimport PromotionSaga from './promotion/saga';\nexport default function* rootSaga() {\n  yield all([AuthSaga(), SearchSaga(), HotelSaga(), RoomSaga(), FeedbackSaga(), ReservationSaga(), ReportFeedbackSaga(), MessageSaga(), PromotionSaga()]);\n}", "map": {"version": 3, "names": ["all", "Auth<PERSON>aga", "SearchSaga", "HotelSaga", "RoomSaga", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ReservationSaga", "ReportFeedbackSaga", "MessageSaga", "PromotionSaga", "rootSaga"], "sources": ["E:/WDP301_UROOM/Customer/src/redux/root-saga.js"], "sourcesContent": ["import 'regenerator-runtime/runtime';\r\nimport {all} from 'redux-saga/effects';\r\nimport AuthSaga from './auth/saga';\r\nimport SearchSaga from './search/saga';\r\nimport HotelSaga from './hotel/saga';\r\nimport RoomSaga from './room/saga';\r\nimport FeedbackSaga from './feedback/saga';\r\nimport ReservationSaga from './reservations/saga';\r\nimport ReportFeedbackSaga from './reportedFeedback/saga';\r\nimport MessageSaga from './message/saga';\r\nimport PromotionSaga from './promotion/saga';\r\n\r\nexport default function* rootSaga() {\r\n  yield all([\r\n    AuthSaga(),\r\n    SearchSaga(),\r\n    HotelSaga(),\r\n    RoomSaga(),\r\n    FeedbackSaga(),\r\n    ReservationSaga(),\r\n    ReportFeedbackSaga(),\r\n    MessageSaga(),\r\n    PromotionSaga(),\r\n  ]);\r\n}\r\n"], "mappings": "AACA,SAAQA,GAAG,QAAO,oBAAoB;AACtC,OAAOC,QAAQ,MAAM,aAAa;AAClC,OAAOC,UAAU,MAAM,eAAe;AACtC,OAAOC,SAAS,MAAM,cAAc;AACpC,OAAOC,QAAQ,MAAM,aAAa;AAClC,OAAOC,YAAY,MAAM,iBAAiB;AAC1C,OAAOC,eAAe,MAAM,qBAAqB;AACjD,OAAOC,kBAAkB,MAAM,yBAAyB;AACxD,OAAOC,WAAW,MAAM,gBAAgB;AACxC,OAAOC,aAAa,MAAM,kBAAkB;AAE5C,eAAe,UAAUC,QAAQA,CAAA,EAAG;EAClC,MAAMV,GAAG,CAAC,CACRC,QAAQ,CAAC,CAAC,EACVC,UAAU,CAAC,CAAC,EACZC,SAAS,CAAC,CAAC,EACXC,QAAQ,CAAC,CAAC,EACVC,YAAY,CAAC,CAAC,EACdC,eAAe,CAAC,CAAC,EACjBC,kBAAkB,CAAC,CAAC,EACpBC,WAAW,CAAC,CAAC,EACbC,aAAa,CAAC,CAAC,CAChB,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}