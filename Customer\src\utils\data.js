import { FaWifi, FaSwimmingPool, FaParking, FaConciergeBell, FaUtensils, FaDumbbell, FaShuttleVan, FaSpa, FaChalkboardTeacher, FaDog, FaWineBottle, FaHandsWash } from "react-icons/fa";

export const cityOptionSelect = [
  { value: "Hà Nội", label: "Hà Nội" },
  { value: "Hồ Chí Minh", label: "Hồ Chí Minh" },
  { value: "Đà Nẵng", label: "Đà Nẵng" },
  { value: "Hải Phòng", label: "Hải Phòng" },
  { value: "Cần Thơ", label: "Cần <PERSON>hơ" },
  { value: "An Giang", label: "An Giang" },
  { value: "Bà Rịa - Vũng Tàu", label: "Bà Rịa - Vũng Tàu" },
  { value: "Bắc Giang", label: "Bắc Giang" },
  { value: "<PERSON>ắc <PERSON>", label: "<PERSON><PERSON><PERSON>" },
  { value: "<PERSON><PERSON><PERSON>", label: "<PERSON><PERSON><PERSON>" },
  { value: "Bắc <PERSON>nh", label: "Bắc <PERSON>" },
  { value: "Bến Tre", label: "Bến Tre" },
  { value: "Bình Định", label: "Bình Định" },
  { value: "Bình Dương", label: "Bình Dương" },
  { value: "Bình Phước", label: "Bình Phước" },
  { value: "Bình Thuận", label: "Bình Thuận" },
  { value: "Cà Mau", label: "Cà Mau" },
  { value: "Cao Bằng", label: "Cao Bằng" },
  { value: "Đắk Lắk", label: "Đắk Lắk" },
  { value: "Đắk Nông", label: "Đắk Nông" },
  { value: "Điện Biên", label: "Điện Biên" },
  { value: "Đồng Nai", label: "Đồng Nai" },
  { value: "Đồng Tháp", label: "Đồng Tháp" },
  { value: "Gia Lai", label: "Gia Lai" },
  { value: "Hà Giang", label: "Hà Giang" },
  { value: "Hà Nam", label: "Hà Nam" },
  { value: "Hà Tĩnh", label: "Hà Tĩnh" },
  { value: "Hải Dương", label: "Hải Dương" },
  { value: "Hậu Giang", label: "Hậu Giang" },
  { value: "Hòa Bình", label: "Hòa Bình" },
  { value: "Hưng Yên", label: "Hưng Yên" },
  { value: "Khánh Hòa", label: "Khánh Hòa" },
  { value: "Kiên Giang", label: "Kiên Giang" },
  { value: "Kon Tum", label: "Kon Tum" },
  { value: "Lai Châu", label: "Lai Châu" },
  { value: "Lâm Đồng", label: "Lâm Đồng" },
  { value: "Lạng Sơn", label: "Lạng Sơn" },
  { value: "Lào Cai", label: "Lào Cai" },
  { value: "Long An", label: "Long An" },
  { value: "Nam Định", label: "Nam Định" },
  { value: "Nghệ An", label: "Nghệ An" },
  { value: "Ninh Bình", label: "Ninh Bình" },
  { value: "Ninh Thuận", label: "Ninh Thuận" },
  { value: "Phú Thọ", label: "Phú Thọ" },
  { value: "Phú Yên", label: "Phú Yên" },
  { value: "Quảng Bình", label: "Quảng Bình" },
  { value: "Quảng Nam", label: "Quảng Nam" },
  { value: "Quảng Ngãi", label: "Quảng Ngãi" },
  { value: "Quảng Ninh", label: "Quảng Ninh" },
  { value: "Quảng Trị", label: "Quảng Trị" },
  { value: "Sóc Trăng", label: "Sóc Trăng" },
  { value: "Sơn La", label: "Sơn La" },
  { value: "Tây Ninh", label: "Tây Ninh" },
  { value: "Thái Bình", label: "Thái Bình" },
  { value: "Thái Nguyên", label: "Thái Nguyên" },
  { value: "Thanh Hóa", label: "Thanh Hóa" },
  { value: "Thừa Thiên Huế", label: "Thừa Thiên Huế" },
  { value: "Tiền Giang", label: "Tiền Giang" },
  { value: "Trà Vinh", label: "Trà Vinh" },
  { value: "Tuyên Quang", label: "Tuyên Quang" },
  { value: "Vĩnh Long", label: "Vĩnh Long" },
  { value: "Vĩnh Phúc", label: "Vĩnh Phúc" },
  { value: "Yên Bái", label: "Yên Bái" }
];

export const districtsByCity = {
  "Đà Nẵng": [
    { value: "Hải Châu", label: "Hải Châu" },
    { value: "Thanh Khê", label: "Thanh Khê" },
    { value: "Sơn Trà", label: "Sơn Trà" },
    { value: "Ngũ Hành Sơn", label: "Ngũ Hành Sơn" },
    { value: "Liên Chiểu", label: "Liên Chiểu" },
    { value: "Cẩm Lệ", label: "Cẩm Lệ" },
    { value: "Hòa Vang", label: "Hòa Vang" },
    { value: "Hoàng Sa", label: "Hoàng Sa" },
  ],
  "Hà Nội": [
    { value: "Ba Đình", label: "Ba Đình" },
    { value: "Hoàn Kiếm", label: "Hoàn Kiếm" },
    { value: "Tây Hồ", label: "Tây Hồ" },
    { value: "Long Biên", label: "Long Biên" },
    { value: "Cầu Giấy", label: "Cầu Giấy" },
    { value: "Đống Đa", label: "Đống Đa" },
    { value: "Hai Bà Trưng", label: "Hai Bà Trưng" },
    { value: "Hoàng Mai", label: "Hoàng Mai" },
    { value: "Thanh Xuân", label: "Thanh Xuân" },
    { value: "Sóc Sơn", label: "Sóc Sơn" },
    { value: "Đông Anh", label: "Đông Anh" },
    { value: "Gia Lâm", label: "Gia Lâm" },
    { value: "Nam Từ Liêm", label: "Nam Từ Liêm" },
    { value: "Bắc Từ Liêm", label: "Bắc Từ Liêm" },
    { value: "Mê Linh", label: "Mê Linh" },
    { value: "Hà Đông", label: "Hà Đông" },
    { value: "Sơn Tây", label: "Sơn Tây" },
    { value: "Ba Vì", label: "Ba Vì" },
    { value: "Phúc Thọ", label: "Phúc Thọ" },
    { value: "Đan Phượng", label: "Đan Phượng" },
    { value: "Hoài Đức", label: "Hoài Đức" },
    { value: "Quốc Oai", label: "Quốc Oai" },
    { value: "Thạch Thất", label: "Thạch Thất" },
    { value: "Chương Mỹ", label: "Chương Mỹ" },
    { value: "Thanh Oai", label: "Thanh Oai" },
    { value: "Thường Tín", label: "Thường Tín" },
    { value: "Phú Xuyên", label: "Phú Xuyên" },
    { value: "Ứng Hòa", label: "Ứng Hòa" },
    { value: "Mỹ Đức", label: "Mỹ Đức" },
  ],
  "Hồ Chí Minh": [
    { value: "Quận 1", label: "Quận 1" },
    { value: "Quận 3", label: "Quận 3" },
    { value: "Quận 4", label: "Quận 4" },
    { value: "Quận 5", label: "Quận 5" },
    { value: "Quận 6", label: "Quận 6" },
    { value: "Quận 7", label: "Quận 7" },
    { value: "Quận 8", label: "Quận 8" },
    { value: "Quận 10", label: "Quận 10" },
    { value: "Quận 11", label: "Quận 11" },
    { value: "Quận 12", label: "Quận 12" },
    { value: "Bình Thạnh", label: "Bình Thạnh" },
    { value: "Tân Bình", label: "Tân Bình" },
    { value: "Tân Phú", label: "Tân Phú" },
    { value: "Phú Nhuận", label: "Phú Nhuận" },
    { value: "Gò Vấp", label: "Gò Vấp" },
    { value: "Bình Tân", label: "Bình Tân" },
    { value: "Thủ Đức", label: "Thủ Đức" },
    { value: "Củ Chi", label: "Củ Chi" },
    { value: "Hóc Môn", label: "Hóc Môn" },
    { value: "Bình Chánh", label: "Bình Chánh" },
    { value: "Nhà Bè", label: "Nhà Bè" },
    { value: "Cần Giờ", label: "Cần Giờ" },
  ],
};


export const listFacilities = [
  { name: "Free Wi-Fi", icon: "FaWifi", description: "Free high-speed internet for guests.", iconTemp: FaWifi },
  { name: "Swimming Pool", icon: "FaSwimmingPool", description: "Spacious, clean, and modern swimming pool.", iconTemp: FaSwimmingPool },
  { name: "Parking Lot", icon: "FaParking", description: "Free parking available for staying guests.", iconTemp: FaParking },
  { name: "24/7 Room Service", icon: "FaConciergeBell", description: "Room service available at all times.", iconTemp: FaConciergeBell },
  { name: "Restaurant", icon: "FaUtensils", description: "Restaurant serving a wide variety of delicious dishes.", iconTemp: FaUtensils },
  { name: "Fitness Center", icon: "FaDumbbell", description: "Gym fully equipped with modern facilities.", iconTemp: FaDumbbell },
  { name: "Airport Shuttle", icon: "FaShuttleVan", description: "Convenient airport transfer service for guests.", iconTemp: FaShuttleVan },
  { name: "Spa & Wellness Center", icon: "FaSpa", description: "Relaxing spa treatments and wellness options.", iconTemp: FaSpa },
  { name: "Laundry Service", icon: "FaHandsWash", description: "Professional laundry and dry-cleaning service.", iconTemp: FaHandsWash },
  { name: "Conference Room", icon: "FaChalkboardTeacher", description: "Spacious and well-equipped conference facilities.", iconTemp: FaChalkboardTeacher },
  { name: "Pet-Friendly", icon: "FaDog", description: "Pets are welcome in designated rooms.", iconTemp: FaDog },
  { name: "Mini Bar", icon: "FaWineBottle", description: "In-room mini bar with snacks and beverages.", iconTemp: FaWineBottle }
];