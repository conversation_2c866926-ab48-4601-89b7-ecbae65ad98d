{"ast": null, "code": "import { combineReducers } from 'redux';\nimport AuthReducer from './auth/reducer';\nimport SearchReducer from './search/reducer';\nimport HotelReducer from './hotel/reducer';\nimport RoomReducer from './room/reducer';\nimport FeedbackReducer from './feedback/reducer';\nimport ReservationReducer from './reservations/reducer';\nimport ReportFeedbackReducer from './reportedFeedback/reducer';\nimport chatboxReducer from './chatbox/reducer';\nimport SocketReducer from './socket/socketSlice';\nimport messageReducer from './message/reducer';\nimport PromotionReducer from './promotion/reducer';\nconst rootReducer = combineReducers({\n  Auth: AuthReducer,\n  Search: SearchReducer,\n  hotel: HotelReducer,\n  Room: RoomReducer,\n  Feedback: FeedbackReducer,\n  Reservation: ReservationReducer,\n  ReportFeedback: ReportFeedbackReducer,\n  ChatBox: chatboxReducer,\n  Socket: SocketReducer,\n  Message: messageReducer,\n  Promotion: PromotionReducer\n});\nexport default rootReducer;", "map": {"version": 3, "names": ["combineReducers", "AuthReducer", "SearchReducer", "HotelReducer", "RoomReducer", "FeedbackReducer", "ReservationReducer", "ReportFeedbackReducer", "chatboxReducer", "SocketReducer", "messageReducer", "PromotionReducer", "rootReducer", "<PERSON><PERSON>", "Search", "hotel", "Room", "<PERSON><PERSON><PERSON>", "Reservation", "ReportFeedback", "ChatBox", "Socket", "Message", "Promotion"], "sources": ["E:/WDP301_UROOM/Customer/src/redux/root-reducer.js"], "sourcesContent": ["import { combineReducers } from 'redux';\r\nimport AuthReducer from './auth/reducer';\r\nimport SearchReducer from './search/reducer';\r\nimport HotelReducer from './hotel/reducer';\r\nimport RoomReducer from './room/reducer';\r\nimport FeedbackReducer from './feedback/reducer';\r\nimport ReservationReducer from './reservations/reducer';\r\nimport ReportFeedbackReducer from './reportedFeedback/reducer';\r\nimport chatboxReducer from './chatbox/reducer';\r\nimport SocketReducer from './socket/socketSlice';\r\nimport messageReducer from './message/reducer';\r\nimport PromotionReducer from './promotion/reducer';\r\n\r\nconst rootReducer = combineReducers({\r\n    Auth: AuthReducer,\r\n    Search: SearchReducer,\r\n    hotel: HotelReducer,\r\n    Room: RoomReducer,\r\n    Feedback:FeedbackReducer,\r\n    Reservation:ReservationReducer,\r\n    ReportFeedback:ReportFeedbackReducer,\r\n    ChatBox: chatboxReducer,\r\n    Socket: SocketReducer,\r\n    Message: messageReducer,\r\n    Promotion: PromotionReducer,\r\n});\r\n\r\nexport default rootReducer;"], "mappings": "AAAA,SAASA,eAAe,QAAQ,OAAO;AACvC,OAAOC,WAAW,MAAM,gBAAgB;AACxC,OAAOC,aAAa,MAAM,kBAAkB;AAC5C,OAAOC,YAAY,MAAM,iBAAiB;AAC1C,OAAOC,WAAW,MAAM,gBAAgB;AACxC,OAAOC,eAAe,MAAM,oBAAoB;AAChD,OAAOC,kBAAkB,MAAM,wBAAwB;AACvD,OAAOC,qBAAqB,MAAM,4BAA4B;AAC9D,OAAOC,cAAc,MAAM,mBAAmB;AAC9C,OAAOC,aAAa,MAAM,sBAAsB;AAChD,OAAOC,cAAc,MAAM,mBAAmB;AAC9C,OAAOC,gBAAgB,MAAM,qBAAqB;AAElD,MAAMC,WAAW,GAAGZ,eAAe,CAAC;EAChCa,IAAI,EAAEZ,WAAW;EACjBa,MAAM,EAAEZ,aAAa;EACrBa,KAAK,EAAEZ,YAAY;EACnBa,IAAI,EAAEZ,WAAW;EACjBa,QAAQ,EAACZ,eAAe;EACxBa,WAAW,EAACZ,kBAAkB;EAC9Ba,cAAc,EAACZ,qBAAqB;EACpCa,OAAO,EAAEZ,cAAc;EACvBa,MAAM,EAAEZ,aAAa;EACrBa,OAAO,EAAEZ,cAAc;EACvBa,SAAS,EAAEZ;AACf,CAAC,CAAC;AAEF,eAAeC,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}