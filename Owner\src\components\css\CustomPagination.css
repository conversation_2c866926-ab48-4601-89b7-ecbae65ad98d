.custom-pagination-container {
  display: flex;
  justify-content: center;
  margin: 20px 0;
}

.custom-pagination {
  background-color: #f0f0f0;
  border-radius: 4px;
  overflow: hidden;
  display: inline-flex;
}

.custom-pagination .page-item {
  margin: 0;
}

.custom-pagination .page-link {
  border: none;
  border-radius: 0;
  color: #333;
  padding: 8px 16px;
  font-weight: 500;
  background-color: transparent;
}

.custom-pagination .page-item:not(:last-child) .page-link {
  border-right: 1px solid #ddd;
}

.custom-pagination .page-item.active .page-link {
  background-color: #007bff;
  color: white;
  border-color: #007bff;
}

.custom-pagination .page-item:not(.active) .page-link:hover {
  background-color: #e9ecef;
}

.pagination-nav-item .page-link {
  color: #777;
}

.pagination-page .page-link {
  min-width: 40px;
  text-align: center;
}
