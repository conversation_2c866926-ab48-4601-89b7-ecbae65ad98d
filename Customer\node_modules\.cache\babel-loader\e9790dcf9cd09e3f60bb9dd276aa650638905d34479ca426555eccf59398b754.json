{"ast": null, "code": "import { all, call, fork, put, takeEvery } from \"@redux-saga/core/effects\";\nimport PromotionActions, { getPromotionsSuccess, getPromotionsFailure, usePromotionSuccess, usePromotionFailure } from \"./actions\";\nimport Factories from \"./factories\";\n\n// 1. <PERSON><PERSON><PERSON> danh sách promotion của người dùng\nfunction* getUserPromotions() {\n  yield takeEvery(PromotionActions.FETCH_USER_PROMOTIONS, function* (action) {\n    const {\n      userId,\n      onSuccess,\n      onFailed,\n      onError\n    } = action.payload;\n    try {\n      var _response$data;\n      const response = yield call(() => Factories.fetchUserPromotions(userId));\n      if ((response === null || response === void 0 ? void 0 : response.status) === 200 && (response === null || response === void 0 ? void 0 : (_response$data = response.data) === null || _response$data === void 0 ? void 0 : _response$data.error) === false) {\n        var _response$data2;\n        let promotions = (_response$data2 = response.data) === null || _response$data2 === void 0 ? void 0 : _response$data2.data;\n\n        // Filter to show only active and upcoming promotions\n        const now = new Date();\n        const relevantPromotions = promotions.filter(promo => {\n          const startDate = new Date(promo.startDate);\n          const endDate = new Date(promo.endDate);\n          if (now < startDate) {\n            return true; // upcoming\n          } else if (now > endDate) {\n            return false; // expired\n          } else if (!promo.isActive) {\n            return false; // inactive\n          } else if (promo.usageLimit && promo.usedCount >= promo.usageLimit) {\n            return false; // used_up\n          } else {\n            return true; // active\n          }\n        });\n        yield put(getPromotionsSuccess({\n          promotions: relevantPromotions,\n          totalCount: relevantPromotions.length\n        }));\n        onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess(relevantPromotions);\n      } else {\n        var _response$data3;\n        const message = (response === null || response === void 0 ? void 0 : (_response$data3 = response.data) === null || _response$data3 === void 0 ? void 0 : _response$data3.message) || \"Không lấy được danh sách khuyến mãi\";\n        yield put(getPromotionsFailure(message));\n        onFailed === null || onFailed === void 0 ? void 0 : onFailed(message);\n      }\n    } catch (error) {\n      var _error$response, _error$response2, _error$response2$data;\n      const status = (_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status;\n      const msg = ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.message) || \"Lỗi server\";\n      yield put(getPromotionsFailure(msg));\n      if (status >= 500) {\n        onError === null || onError === void 0 ? void 0 : onError(error);\n      } else {\n        onFailed === null || onFailed === void 0 ? void 0 : onFailed(msg);\n      }\n    }\n  });\n}\n\n// 2. Sử dụng promotion\nfunction* applyPromotion() {\n  var _s = $RefreshSig$();\n  yield _s(takeEvery(PromotionActions.USE_PROMOTION, _s(function* (action) {\n    _s();\n    const {\n      promotionId,\n      data,\n      onSuccess,\n      onFailed,\n      onError\n    } = action.payload;\n    try {\n      var _response$data4;\n      const response = yield call(() => Factories.applyPromotion(promotionId, data));\n      if ((response === null || response === void 0 ? void 0 : response.status) === 200 && (response === null || response === void 0 ? void 0 : (_response$data4 = response.data) === null || _response$data4 === void 0 ? void 0 : _response$data4.error) === false) {\n        var _response$data5;\n        const updatedPromotion = (_response$data5 = response.data) === null || _response$data5 === void 0 ? void 0 : _response$data5.data;\n        yield put(usePromotionSuccess(updatedPromotion));\n        onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess(updatedPromotion);\n      } else {\n        var _response$data6;\n        const message = (response === null || response === void 0 ? void 0 : (_response$data6 = response.data) === null || _response$data6 === void 0 ? void 0 : _response$data6.message) || \"Không thể sử dụng khuyến mãi\";\n        yield put(usePromotionFailure(message));\n        onFailed === null || onFailed === void 0 ? void 0 : onFailed(message);\n      }\n    } catch (error) {\n      var _error$response3, _error$response4, _error$response4$data;\n      const status = (_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : _error$response3.status;\n      const msg = ((_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : (_error$response4$data = _error$response4.data) === null || _error$response4$data === void 0 ? void 0 : _error$response4$data.message) || \"Lỗi server\";\n      yield put(usePromotionFailure(msg));\n      if (status >= 500) {\n        onError === null || onError === void 0 ? void 0 : onError(error);\n      } else {\n        onFailed === null || onFailed === void 0 ? void 0 : onFailed(msg);\n      }\n    }\n  }, \"gVDSAc9HmBRJWI0qZYaKqtNUTRg=\", false, function () {\n    return [usePromotionSuccess, usePromotionFailure, usePromotionFailure];\n  })), \"gVDSAc9HmBRJWI0qZYaKqtNUTRg=\", false, function () {\n    return [usePromotionSuccess, usePromotionFailure, usePromotionFailure];\n  });\n}\nexport default function* promotionSaga() {\n  yield all([fork(getUserPromotions), fork(applyPromotion)]);\n}", "map": {"version": 3, "names": ["all", "call", "fork", "put", "takeEvery", "PromotionActions", "getPromotionsSuccess", "getPromotionsFailure", "usePromotionSuccess", "usePromotionFailure", "Factories", "getUserPromotions", "FETCH_USER_PROMOTIONS", "action", "userId", "onSuccess", "onFailed", "onError", "payload", "_response$data", "response", "fetchUserPromotions", "status", "data", "error", "_response$data2", "promotions", "now", "Date", "relevantPromotions", "filter", "promo", "startDate", "endDate", "isActive", "usageLimit", "usedCount", "totalCount", "length", "_response$data3", "message", "_error$response", "_error$response2", "_error$response2$data", "msg", "applyPromotion", "_s", "$RefreshSig$", "USE_PROMOTION", "promotionId", "_response$data4", "_response$data5", "updatedPromotion", "_response$data6", "_error$response3", "_error$response4", "_error$response4$data", "promotionSaga"], "sources": ["E:/WDP301_UROOM/Customer/src/redux/promotion/saga.js"], "sourcesContent": ["import { all, call, fork, put, takeEvery } from \"@redux-saga/core/effects\";\r\nimport PromotionActions, { getPromotionsSuccess, getPromotionsFailure, usePromotionSuccess, usePromotionFailure } from \"./actions\";\r\nimport Factories from \"./factories\";\r\n\r\n// 1. L<PERSON>y danh sách promotion của người dùng\r\nfunction* getUserPromotions() {\r\n  yield takeEvery(PromotionActions.FETCH_USER_PROMOTIONS, function* (action) {\r\n    const { userId, onSuccess, onFailed, onError } = action.payload;\r\n\r\n    try {\r\n      const response = yield call(() => Factories.fetchUserPromotions(userId));\r\n\r\n      if (response?.status === 200 && response?.data?.error === false) {\r\n        let promotions = response.data?.data;\r\n        \r\n        // Filter to show only active and upcoming promotions\r\n        const now = new Date();\r\n        const relevantPromotions = promotions.filter(promo => {\r\n          const startDate = new Date(promo.startDate);\r\n          const endDate = new Date(promo.endDate);\r\n          \r\n          if (now < startDate) {\r\n            return true; // upcoming\r\n          } else if (now > endDate) {\r\n            return false; // expired\r\n          } else if (!promo.isActive) {\r\n            return false; // inactive\r\n          } else if (promo.usageLimit && promo.usedCount >= promo.usageLimit) {\r\n            return false; // used_up\r\n          } else {\r\n            return true; // active\r\n          }\r\n        });\r\n        \r\n        yield put(getPromotionsSuccess({\r\n          promotions: relevantPromotions,\r\n          totalCount: relevantPromotions.length\r\n        }));\r\n        onSuccess?.(relevantPromotions);\r\n      } else {\r\n        const message = response?.data?.message || \"Không lấy được danh sách khuyến mãi\";\r\n        yield put(getPromotionsFailure(message));\r\n        onFailed?.(message);\r\n      }\r\n    } catch (error) {\r\n      const status = error.response?.status;\r\n      const msg = error.response?.data?.message || \"Lỗi server\";\r\n      \r\n      yield put(getPromotionsFailure(msg));\r\n\r\n      if (status >= 500) {\r\n        onError?.(error);\r\n      } else {\r\n        onFailed?.(msg);\r\n      }\r\n    }\r\n  });\r\n}\r\n\r\n// 2. Sử dụng promotion\r\nfunction* applyPromotion() {\r\n  yield takeEvery(PromotionActions.USE_PROMOTION, function* (action) {\r\n    const { promotionId, data, onSuccess, onFailed, onError } = action.payload;\r\n\r\n    try {\r\n      const response = yield call(() => Factories.applyPromotion(promotionId, data));\r\n\r\n      if (response?.status === 200 && response?.data?.error === false) {\r\n        const updatedPromotion = response.data?.data;\r\n        yield put(usePromotionSuccess(updatedPromotion));\r\n        onSuccess?.(updatedPromotion);\r\n      } else {\r\n        const message = response?.data?.message || \"Không thể sử dụng khuyến mãi\";\r\n        yield put(usePromotionFailure(message));\r\n        onFailed?.(message);\r\n      }\r\n    } catch (error) {\r\n      const status = error.response?.status;\r\n      const msg = error.response?.data?.message || \"Lỗi server\";\r\n      \r\n      yield put(usePromotionFailure(msg));\r\n\r\n      if (status >= 500) {\r\n        onError?.(error);\r\n      } else {\r\n        onFailed?.(msg);\r\n      }\r\n    }\r\n  });\r\n}\r\n\r\nexport default function* promotionSaga() {\r\n  yield all([\r\n    fork(getUserPromotions),\r\n    fork(applyPromotion),\r\n  ]);\r\n}\r\n"], "mappings": "AAAA,SAASA,GAAG,EAAEC,IAAI,EAAEC,IAAI,EAAEC,GAAG,EAAEC,SAAS,QAAQ,0BAA0B;AAC1E,OAAOC,gBAAgB,IAAIC,oBAAoB,EAAEC,oBAAoB,EAAEC,mBAAmB,EAAEC,mBAAmB,QAAQ,WAAW;AAClI,OAAOC,SAAS,MAAM,aAAa;;AAEnC;AACA,UAAUC,iBAAiBA,CAAA,EAAG;EAC5B,MAAMP,SAAS,CAACC,gBAAgB,CAACO,qBAAqB,EAAE,WAAWC,MAAM,EAAE;IACzE,MAAM;MAAEC,MAAM;MAAEC,SAAS;MAAEC,QAAQ;MAAEC;IAAQ,CAAC,GAAGJ,MAAM,CAACK,OAAO;IAE/D,IAAI;MAAA,IAAAC,cAAA;MACF,MAAMC,QAAQ,GAAG,MAAMnB,IAAI,CAAC,MAAMS,SAAS,CAACW,mBAAmB,CAACP,MAAM,CAAC,CAAC;MAExE,IAAI,CAAAM,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEE,MAAM,MAAK,GAAG,IAAI,CAAAF,QAAQ,aAARA,QAAQ,wBAAAD,cAAA,GAARC,QAAQ,CAAEG,IAAI,cAAAJ,cAAA,uBAAdA,cAAA,CAAgBK,KAAK,MAAK,KAAK,EAAE;QAAA,IAAAC,eAAA;QAC/D,IAAIC,UAAU,IAAAD,eAAA,GAAGL,QAAQ,CAACG,IAAI,cAAAE,eAAA,uBAAbA,eAAA,CAAeF,IAAI;;QAEpC;QACA,MAAMI,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC;QACtB,MAAMC,kBAAkB,GAAGH,UAAU,CAACI,MAAM,CAACC,KAAK,IAAI;UACpD,MAAMC,SAAS,GAAG,IAAIJ,IAAI,CAACG,KAAK,CAACC,SAAS,CAAC;UAC3C,MAAMC,OAAO,GAAG,IAAIL,IAAI,CAACG,KAAK,CAACE,OAAO,CAAC;UAEvC,IAAIN,GAAG,GAAGK,SAAS,EAAE;YACnB,OAAO,IAAI,CAAC,CAAC;UACf,CAAC,MAAM,IAAIL,GAAG,GAAGM,OAAO,EAAE;YACxB,OAAO,KAAK,CAAC,CAAC;UAChB,CAAC,MAAM,IAAI,CAACF,KAAK,CAACG,QAAQ,EAAE;YAC1B,OAAO,KAAK,CAAC,CAAC;UAChB,CAAC,MAAM,IAAIH,KAAK,CAACI,UAAU,IAAIJ,KAAK,CAACK,SAAS,IAAIL,KAAK,CAACI,UAAU,EAAE;YAClE,OAAO,KAAK,CAAC,CAAC;UAChB,CAAC,MAAM;YACL,OAAO,IAAI,CAAC,CAAC;UACf;QACF,CAAC,CAAC;QAEF,MAAMhC,GAAG,CAACG,oBAAoB,CAAC;UAC7BoB,UAAU,EAAEG,kBAAkB;UAC9BQ,UAAU,EAAER,kBAAkB,CAACS;QACjC,CAAC,CAAC,CAAC;QACHvB,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAGc,kBAAkB,CAAC;MACjC,CAAC,MAAM;QAAA,IAAAU,eAAA;QACL,MAAMC,OAAO,GAAG,CAAApB,QAAQ,aAARA,QAAQ,wBAAAmB,eAAA,GAARnB,QAAQ,CAAEG,IAAI,cAAAgB,eAAA,uBAAdA,eAAA,CAAgBC,OAAO,KAAI,qCAAqC;QAChF,MAAMrC,GAAG,CAACI,oBAAoB,CAACiC,OAAO,CAAC,CAAC;QACxCxB,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAGwB,OAAO,CAAC;MACrB;IACF,CAAC,CAAC,OAAOhB,KAAK,EAAE;MAAA,IAAAiB,eAAA,EAAAC,gBAAA,EAAAC,qBAAA;MACd,MAAMrB,MAAM,IAAAmB,eAAA,GAAGjB,KAAK,CAACJ,QAAQ,cAAAqB,eAAA,uBAAdA,eAAA,CAAgBnB,MAAM;MACrC,MAAMsB,GAAG,GAAG,EAAAF,gBAAA,GAAAlB,KAAK,CAACJ,QAAQ,cAAAsB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBnB,IAAI,cAAAoB,qBAAA,uBAApBA,qBAAA,CAAsBH,OAAO,KAAI,YAAY;MAEzD,MAAMrC,GAAG,CAACI,oBAAoB,CAACqC,GAAG,CAAC,CAAC;MAEpC,IAAItB,MAAM,IAAI,GAAG,EAAE;QACjBL,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAGO,KAAK,CAAC;MAClB,CAAC,MAAM;QACLR,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAG4B,GAAG,CAAC;MACjB;IACF;EACF,CAAC,CAAC;AACJ;;AAEA;AACA,UAAUC,cAAcA,CAAA,EAAG;EAAA,IAAAC,EAAA,GAAAC,YAAA;EACzB,MAAAD,EAAA,CAAM1C,SAAS,CAACC,gBAAgB,CAAC2C,aAAa,EAAAF,EAAA,CAAE,WAAWjC,MAAM,EAAE;IAAAiC,EAAA;IACjE,MAAM;MAAEG,WAAW;MAAE1B,IAAI;MAAER,SAAS;MAAEC,QAAQ;MAAEC;IAAQ,CAAC,GAAGJ,MAAM,CAACK,OAAO;IAE1E,IAAI;MAAA,IAAAgC,eAAA;MACF,MAAM9B,QAAQ,GAAG,MAAMnB,IAAI,CAAC,MAAMS,SAAS,CAACmC,cAAc,CAACI,WAAW,EAAE1B,IAAI,CAAC,CAAC;MAE9E,IAAI,CAAAH,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEE,MAAM,MAAK,GAAG,IAAI,CAAAF,QAAQ,aAARA,QAAQ,wBAAA8B,eAAA,GAAR9B,QAAQ,CAAEG,IAAI,cAAA2B,eAAA,uBAAdA,eAAA,CAAgB1B,KAAK,MAAK,KAAK,EAAE;QAAA,IAAA2B,eAAA;QAC/D,MAAMC,gBAAgB,IAAAD,eAAA,GAAG/B,QAAQ,CAACG,IAAI,cAAA4B,eAAA,uBAAbA,eAAA,CAAe5B,IAAI;QAC5C,MAAMpB,GAAG,CAACK,mBAAmB,CAAC4C,gBAAgB,CAAC,CAAC;QAChDrC,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAGqC,gBAAgB,CAAC;MAC/B,CAAC,MAAM;QAAA,IAAAC,eAAA;QACL,MAAMb,OAAO,GAAG,CAAApB,QAAQ,aAARA,QAAQ,wBAAAiC,eAAA,GAARjC,QAAQ,CAAEG,IAAI,cAAA8B,eAAA,uBAAdA,eAAA,CAAgBb,OAAO,KAAI,8BAA8B;QACzE,MAAMrC,GAAG,CAACM,mBAAmB,CAAC+B,OAAO,CAAC,CAAC;QACvCxB,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAGwB,OAAO,CAAC;MACrB;IACF,CAAC,CAAC,OAAOhB,KAAK,EAAE;MAAA,IAAA8B,gBAAA,EAAAC,gBAAA,EAAAC,qBAAA;MACd,MAAMlC,MAAM,IAAAgC,gBAAA,GAAG9B,KAAK,CAACJ,QAAQ,cAAAkC,gBAAA,uBAAdA,gBAAA,CAAgBhC,MAAM;MACrC,MAAMsB,GAAG,GAAG,EAAAW,gBAAA,GAAA/B,KAAK,CAACJ,QAAQ,cAAAmC,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBhC,IAAI,cAAAiC,qBAAA,uBAApBA,qBAAA,CAAsBhB,OAAO,KAAI,YAAY;MAEzD,MAAMrC,GAAG,CAACM,mBAAmB,CAACmC,GAAG,CAAC,CAAC;MAEnC,IAAItB,MAAM,IAAI,GAAG,EAAE;QACjBL,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAGO,KAAK,CAAC;MAClB,CAAC,MAAM;QACLR,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAG4B,GAAG,CAAC;MACjB;IACF;EACF,CAAC;IAAA,QAnBepC,mBAAmB,EAInBC,mBAAmB,EAOrBA,mBAAmB;EAAA,EAQhC,CAAC;IAAA,QAnBcD,mBAAmB,EAInBC,mBAAmB,EAOrBA,mBAAmB;EAAA,EAQ/B;AACJ;AAEA,eAAe,UAAUgD,aAAaA,CAAA,EAAG;EACvC,MAAMzD,GAAG,CAAC,CACRE,IAAI,CAACS,iBAAiB,CAAC,EACvBT,IAAI,CAAC2C,cAAc,CAAC,CACrB,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}