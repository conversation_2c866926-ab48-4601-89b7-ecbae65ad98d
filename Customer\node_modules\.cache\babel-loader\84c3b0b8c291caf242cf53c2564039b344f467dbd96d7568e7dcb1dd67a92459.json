{"ast": null, "code": "const PromotionActions = {\n  FETCH_USER_PROMOTIONS: \"FETCH_USER_PROMOTIONS\",\n  FETCH_USER_PROMOTIONS_SUCCESS: \"FETCH_USER_PROMOTIONS_SUCCESS\",\n  FETCH_USER_PROMOTIONS_FAILURE: \"FETCH_USER_PROMOTIONS_FAILURE\",\n  USE_PROMOTION: \"USE_PROMOTION\",\n  USE_PROMOTION_SUCCESS: \"USE_PROMOTION_SUCCESS\",\n  USE_PROMOTION_FAILURE: \"USE_PROMOTION_FAILURE\"\n};\n\n// Action creators\nexport const getPromotions = params => ({\n  type: PromotionActions.FETCH_USER_PROMOTIONS,\n  payload: params\n});\nexport const getPromotionsSuccess = data => ({\n  type: PromotionActions.FETCH_USER_PROMOTIONS_SUCCESS,\n  payload: data\n});\nexport const getPromotionsFailure = error => ({\n  type: PromotionActions.FETCH_USER_PROMOTIONS_FAILURE,\n  payload: error\n});\nexport const usePromotion = (promotionId, data) => ({\n  type: PromotionActions.USE_PROMOTION,\n  payload: {\n    promotionId,\n    data\n  }\n});\nexport const usePromotionSuccess = data => ({\n  type: PromotionActions.USE_PROMOTION_SUCCESS,\n  payload: data\n});\nexport const usePromotionFailure = error => ({\n  type: PromotionActions.USE_PROMOTION_FAILURE,\n  payload: error\n});\nexport default PromotionActions;", "map": {"version": 3, "names": ["PromotionActions", "FETCH_USER_PROMOTIONS", "FETCH_USER_PROMOTIONS_SUCCESS", "FETCH_USER_PROMOTIONS_FAILURE", "USE_PROMOTION", "USE_PROMOTION_SUCCESS", "USE_PROMOTION_FAILURE", "getPromotions", "params", "type", "payload", "getPromotionsSuccess", "data", "getPromotionsFailure", "error", "usePromotion", "promotionId", "usePromotionSuccess", "usePromotionFailure"], "sources": ["E:/WDP301_UROOM/Customer/src/redux/promotion/actions.js"], "sourcesContent": ["const PromotionActions = {\r\n  FETCH_USER_PROMOTIONS: \"FETCH_USER_PROMOTIONS\",\r\n  FETCH_USER_PROMOTIONS_SUCCESS: \"FETCH_USER_PROMOTIONS_SUCCESS\",\r\n  FETCH_USER_PROMOTIONS_FAILURE: \"FETCH_USER_PROMOTIONS_FAILURE\",\r\n  USE_PROMOTION: \"USE_PROMOTION\",\r\n  USE_PROMOTION_SUCCESS: \"USE_PROMOTION_SUCCESS\",\r\n  USE_PROMOTION_FAILURE: \"USE_PROMOTION_FAILURE\",\r\n};\r\n\r\n// Action creators\r\nexport const getPromotions = (params) => ({\r\n  type: PromotionActions.FETCH_USER_PROMOTIONS,\r\n  payload: params\r\n});\r\n\r\nexport const getPromotionsSuccess = (data) => ({\r\n  type: PromotionActions.FETCH_USER_PROMOTIONS_SUCCESS,\r\n  payload: data\r\n});\r\n\r\nexport const getPromotionsFailure = (error) => ({\r\n  type: PromotionActions.FETCH_USER_PROMOTIONS_FAILURE,\r\n  payload: error\r\n});\r\n\r\nexport const usePromotion = (promotionId, data) => ({\r\n  type: PromotionActions.USE_PROMOTION,\r\n  payload: { promotionId, data }\r\n});\r\n\r\nexport const usePromotionSuccess = (data) => ({\r\n  type: PromotionActions.USE_PROMOTION_SUCCESS,\r\n  payload: data\r\n});\r\n\r\nexport const usePromotionFailure = (error) => ({\r\n  type: PromotionActions.USE_PROMOTION_FAILURE,\r\n  payload: error\r\n});\r\n\r\nexport default PromotionActions;\r\n"], "mappings": "AAAA,MAAMA,gBAAgB,GAAG;EACvBC,qBAAqB,EAAE,uBAAuB;EAC9CC,6BAA6B,EAAE,+BAA+B;EAC9DC,6BAA6B,EAAE,+BAA+B;EAC9DC,aAAa,EAAE,eAAe;EAC9BC,qBAAqB,EAAE,uBAAuB;EAC9CC,qBAAqB,EAAE;AACzB,CAAC;;AAED;AACA,OAAO,MAAMC,aAAa,GAAIC,MAAM,KAAM;EACxCC,IAAI,EAAET,gBAAgB,CAACC,qBAAqB;EAC5CS,OAAO,EAAEF;AACX,CAAC,CAAC;AAEF,OAAO,MAAMG,oBAAoB,GAAIC,IAAI,KAAM;EAC7CH,IAAI,EAAET,gBAAgB,CAACE,6BAA6B;EACpDQ,OAAO,EAAEE;AACX,CAAC,CAAC;AAEF,OAAO,MAAMC,oBAAoB,GAAIC,KAAK,KAAM;EAC9CL,IAAI,EAAET,gBAAgB,CAACG,6BAA6B;EACpDO,OAAO,EAAEI;AACX,CAAC,CAAC;AAEF,OAAO,MAAMC,YAAY,GAAGA,CAACC,WAAW,EAAEJ,IAAI,MAAM;EAClDH,IAAI,EAAET,gBAAgB,CAACI,aAAa;EACpCM,OAAO,EAAE;IAAEM,WAAW;IAAEJ;EAAK;AAC/B,CAAC,CAAC;AAEF,OAAO,MAAMK,mBAAmB,GAAIL,IAAI,KAAM;EAC5CH,IAAI,EAAET,gBAAgB,CAACK,qBAAqB;EAC5CK,OAAO,EAAEE;AACX,CAAC,CAAC;AAEF,OAAO,MAAMM,mBAAmB,GAAIJ,KAAK,KAAM;EAC7CL,IAAI,EAAET,gBAAgB,CAACM,qBAAqB;EAC5CI,OAAO,EAAEI;AACX,CAAC,CAAC;AAEF,eAAed,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}