{"ast": null, "code": "const PromotionActions = {\n  FETCH_USER_PROMOTIONS: \"FETCH_USER_PROMOTIONS\",\n  FETCH_USER_PROMOTIONS_SUCCESS: \"FETCH_USER_PROMOTIONS_SUCCESS\",\n  FETCH_USER_PROMOTIONS_FAILURE: \"FETCH_USER_PROMOTIONS_FAILURE\",\n  USE_PROMOTION: \"USE_PROMOTION\",\n  USE_PROMOTION_SUCCESS: \"USE_PROMOTION_SUCCESS\",\n  USE_PROMOTION_FAILURE: \"USE_PROMOTION_FAILURE\"\n};\nexport default PromotionActions;", "map": {"version": 3, "names": ["PromotionActions", "FETCH_USER_PROMOTIONS", "FETCH_USER_PROMOTIONS_SUCCESS", "FETCH_USER_PROMOTIONS_FAILURE", "USE_PROMOTION", "USE_PROMOTION_SUCCESS", "USE_PROMOTION_FAILURE"], "sources": ["E:/WDP301_UROOM/Customer/src/redux/promotion/actions.js"], "sourcesContent": ["const PromotionActions = {\r\n  FETCH_USER_PROMOTIONS: \"FETCH_USER_PROMOTIONS\",\r\n  FETCH_USER_PROMOTIONS_SUCCESS: \"FETCH_USER_PROMOTIONS_SUCCESS\",\r\n  FETCH_USER_PROMOTIONS_FAILURE: \"FETCH_USER_PROMOTIONS_FAILURE\",\r\n  USE_PROMOTION: \"USE_PROMOTION\",\r\n  USE_PROMOTION_SUCCESS: \"USE_PROMOTION_SUCCESS\",\r\n  USE_PROMOTION_FAILURE: \"USE_PROMOTION_FAILURE\",\r\n};\r\n\r\nexport default PromotionActions;\r\n"], "mappings": "AAAA,MAAMA,gBAAgB,GAAG;EACvBC,qBAAqB,EAAE,uBAAuB;EAC9CC,6BAA6B,EAAE,+BAA+B;EAC9DC,6BAA6B,EAAE,+BAA+B;EAC9DC,aAAa,EAAE,eAAe;EAC9BC,qBAAqB,EAAE,uBAAuB;EAC9CC,qBAAqB,EAAE;AACzB,CAAC;AAED,eAAeN,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}