import React from 'react';
import { Provider } from 'react-redux';
import { <PERSON>rowserRouter } from 'react-router-dom';
import store from '../redux/store';
import MyPromotion from '../pages/customer/information/components/MyPromotion';

const MyPromotionTest = () => {
  return (
    <Provider store={store}>
      <BrowserRouter>
        <div style={{ padding: '20px' }}>
          <h1>Testing MyPromotion with Redux</h1>
          <MyPromotion />
        </div>
      </BrowserRouter>
    </Provider>
  );
};

export default MyPromotionTest;
