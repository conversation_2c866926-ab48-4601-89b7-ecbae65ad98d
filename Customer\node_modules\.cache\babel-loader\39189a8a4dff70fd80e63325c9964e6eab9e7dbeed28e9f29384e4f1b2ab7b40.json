{"ast": null, "code": "import PromotionActions from \"./actions\";\n\n// Mock data for demo purposes\nconst mockPromotions = [{\n  _id: \"1\",\n  code: \"SAVE20\",\n  name: \"Save $20 Deal\",\n  description: \"Save $20 on orders over $100\",\n  discountType: \"FIXED_AMOUNT\",\n  discountValue: 20,\n  minOrderAmount: 100,\n  maxDiscountAmount: 20,\n  startDate: \"2025-01-01\",\n  endDate: \"2025-12-31\",\n  isActive: true,\n  usageLimit: 100,\n  usedCount: 25\n}, {\n  _id: \"2\",\n  code: \"PERCENT10\",\n  name: \"10% Off Everything\",\n  description: \"10% off on all bookings\",\n  discountType: \"PERCENTAGE\",\n  discountValue: 10,\n  minOrderAmount: 50,\n  maxDiscountAmount: 50,\n  startDate: \"2025-01-01\",\n  endDate: \"2025-12-31\",\n  isActive: true,\n  usageLimit: null,\n  usedCount: 0\n}, {\n  _id: \"3\",\n  code: \"SUMMER25\",\n  name: \"Summer Special\",\n  description: \"25% off summer bookings - Starting July 1st\",\n  discountType: \"PERCENTAGE\",\n  discountValue: 25,\n  minOrderAmount: 200,\n  maxDiscountAmount: 100,\n  startDate: \"2025-07-01\",\n  endDate: \"2025-08-31\",\n  isActive: true,\n  usageLimit: 50,\n  usedCount: 0\n}, {\n  _id: \"4\",\n  code: \"NEWUSER30\",\n  name: \"New User Bonus\",\n  description: \"$30 off for new customers - Coming soon!\",\n  discountType: \"FIXED_AMOUNT\",\n  discountValue: 30,\n  minOrderAmount: 150,\n  maxDiscountAmount: 30,\n  startDate: \"2025-08-01\",\n  endDate: \"2025-12-31\",\n  isActive: true,\n  usageLimit: 200,\n  usedCount: 0\n}];\n\n// Filter mock data to show only active and upcoming\nconst now = new Date();\nconst filteredMockPromotions = mockPromotions.filter(promo => {\n  const startDate = new Date(promo.startDate);\n  const endDate = new Date(promo.endDate);\n  if (now < startDate) {\n    return true; // upcoming\n  } else if (now > endDate) {\n    return false; // expired\n  } else if (!promo.isActive) {\n    return false; // inactive\n  } else if (promo.usageLimit && promo.usedCount >= promo.usageLimit) {\n    return false; // used_up\n  } else {\n    return true; // active\n  }\n});\nconst initialState = {\n  promotions: filteredMockPromotions,\n  loading: false,\n  error: null\n};\nconst promotionReducer = (state = initialState, action) => {\n  switch (action.type) {\n    case PromotionActions.FETCH_USER_PROMOTIONS:\n      return {\n        ...state,\n        loading: true,\n        error: null\n      };\n    case PromotionActions.FETCH_USER_PROMOTIONS_SUCCESS:\n      return {\n        ...state,\n        loading: false,\n        promotions: action.payload,\n        error: null\n      };\n    case PromotionActions.FETCH_USER_PROMOTIONS_FAILURE:\n      return {\n        ...state,\n        loading: false,\n        error: action.payload\n      };\n    case PromotionActions.USE_PROMOTION_SUCCESS:\n      return {\n        ...state,\n        promotions: state.promotions.map(promotion => promotion._id === action.payload._id ? {\n          ...promotion,\n          isUsed: true,\n          usedAt: new Date().toISOString()\n        } : promotion)\n      };\n    default:\n      return state;\n  }\n};\nexport default promotionReducer;", "map": {"version": 3, "names": ["PromotionActions", "mockPromotions", "_id", "code", "name", "description", "discountType", "discountValue", "minOrderAmount", "maxDiscountAmount", "startDate", "endDate", "isActive", "usageLimit", "usedCount", "now", "Date", "filteredMockPromotions", "filter", "promo", "initialState", "promotions", "loading", "error", "promotionReducer", "state", "action", "type", "FETCH_USER_PROMOTIONS", "FETCH_USER_PROMOTIONS_SUCCESS", "payload", "FETCH_USER_PROMOTIONS_FAILURE", "USE_PROMOTION_SUCCESS", "map", "promotion", "isUsed", "usedAt", "toISOString"], "sources": ["E:/WDP301_UROOM/Customer/src/redux/promotion/reducer.js"], "sourcesContent": ["import PromotionActions from \"./actions\";\r\n\r\n// Mock data for demo purposes\r\nconst mockPromotions = [\r\n  {\r\n    _id: \"1\",\r\n    code: \"SAVE20\",\r\n    name: \"Save $20 Deal\",\r\n    description: \"Save $20 on orders over $100\",\r\n    discountType: \"FIXED_AMOUNT\",\r\n    discountValue: 20,\r\n    minOrderAmount: 100,\r\n    maxDiscountAmount: 20,\r\n    startDate: \"2025-01-01\",\r\n    endDate: \"2025-12-31\",\r\n    isActive: true,\r\n    usageLimit: 100,\r\n    usedCount: 25\r\n  },\r\n  {\r\n    _id: \"2\",\r\n    code: \"PERCENT10\",\r\n    name: \"10% Off Everything\",\r\n    description: \"10% off on all bookings\",\r\n    discountType: \"PERCENTAGE\",\r\n    discountValue: 10,\r\n    minOrderAmount: 50,\r\n    maxDiscountAmount: 50,\r\n    startDate: \"2025-01-01\",\r\n    endDate: \"2025-12-31\",\r\n    isActive: true,\r\n    usageLimit: null,\r\n    usedCount: 0\r\n  },\r\n  {\r\n    _id: \"3\",\r\n    code: \"SUMMER25\",\r\n    name: \"Summer Special\",\r\n    description: \"25% off summer bookings - Starting July 1st\",\r\n    discountType: \"PERCENTAGE\",\r\n    discountValue: 25,\r\n    minOrderAmount: 200,\r\n    maxDiscountAmount: 100,\r\n    startDate: \"2025-07-01\",\r\n    endDate: \"2025-08-31\",\r\n    isActive: true,\r\n    usageLimit: 50,\r\n    usedCount: 0\r\n  },\r\n  {\r\n    _id: \"4\",\r\n    code: \"NEWUSER30\",\r\n    name: \"New User Bonus\",\r\n    description: \"$30 off for new customers - Coming soon!\",\r\n    discountType: \"FIXED_AMOUNT\",\r\n    discountValue: 30,\r\n    minOrderAmount: 150,\r\n    maxDiscountAmount: 30,\r\n    startDate: \"2025-08-01\",\r\n    endDate: \"2025-12-31\",\r\n    isActive: true,\r\n    usageLimit: 200,\r\n    usedCount: 0\r\n  }\r\n];\r\n\r\n// Filter mock data to show only active and upcoming\r\nconst now = new Date();\r\nconst filteredMockPromotions = mockPromotions.filter(promo => {\r\n  const startDate = new Date(promo.startDate);\r\n  const endDate = new Date(promo.endDate);\r\n  \r\n  if (now < startDate) {\r\n    return true; // upcoming\r\n  } else if (now > endDate) {\r\n    return false; // expired\r\n  } else if (!promo.isActive) {\r\n    return false; // inactive\r\n  } else if (promo.usageLimit && promo.usedCount >= promo.usageLimit) {\r\n    return false; // used_up\r\n  } else {\r\n    return true; // active\r\n  }\r\n});\r\n\r\nconst initialState = {\r\n  promotions: filteredMockPromotions,\r\n  loading: false,\r\n  error: null,\r\n};\r\n\r\nconst promotionReducer = (state = initialState, action) => {\r\n  switch (action.type) {\r\n    case PromotionActions.FETCH_USER_PROMOTIONS:\r\n      return {\r\n        ...state,\r\n        loading: true,\r\n        error: null,\r\n      };\r\n\r\n    case PromotionActions.FETCH_USER_PROMOTIONS_SUCCESS:\r\n      return {\r\n        ...state,\r\n        loading: false,\r\n        promotions: action.payload,\r\n        error: null,\r\n      };\r\n\r\n    case PromotionActions.FETCH_USER_PROMOTIONS_FAILURE:\r\n      return {\r\n        ...state,\r\n        loading: false,\r\n        error: action.payload,\r\n      };\r\n\r\n    case PromotionActions.USE_PROMOTION_SUCCESS:\r\n      return {\r\n        ...state,\r\n        promotions: state.promotions.map((promotion) =>\r\n          promotion._id === action.payload._id \r\n            ? { ...promotion, isUsed: true, usedAt: new Date().toISOString() }\r\n            : promotion\r\n        ),\r\n      };\r\n\r\n    default:\r\n      return state;\r\n  }\r\n};\r\n\r\nexport default promotionReducer;\r\n"], "mappings": "AAAA,OAAOA,gBAAgB,MAAM,WAAW;;AAExC;AACA,MAAMC,cAAc,GAAG,CACrB;EACEC,GAAG,EAAE,GAAG;EACRC,IAAI,EAAE,QAAQ;EACdC,IAAI,EAAE,eAAe;EACrBC,WAAW,EAAE,8BAA8B;EAC3CC,YAAY,EAAE,cAAc;EAC5BC,aAAa,EAAE,EAAE;EACjBC,cAAc,EAAE,GAAG;EACnBC,iBAAiB,EAAE,EAAE;EACrBC,SAAS,EAAE,YAAY;EACvBC,OAAO,EAAE,YAAY;EACrBC,QAAQ,EAAE,IAAI;EACdC,UAAU,EAAE,GAAG;EACfC,SAAS,EAAE;AACb,CAAC,EACD;EACEZ,GAAG,EAAE,GAAG;EACRC,IAAI,EAAE,WAAW;EACjBC,IAAI,EAAE,oBAAoB;EAC1BC,WAAW,EAAE,yBAAyB;EACtCC,YAAY,EAAE,YAAY;EAC1BC,aAAa,EAAE,EAAE;EACjBC,cAAc,EAAE,EAAE;EAClBC,iBAAiB,EAAE,EAAE;EACrBC,SAAS,EAAE,YAAY;EACvBC,OAAO,EAAE,YAAY;EACrBC,QAAQ,EAAE,IAAI;EACdC,UAAU,EAAE,IAAI;EAChBC,SAAS,EAAE;AACb,CAAC,EACD;EACEZ,GAAG,EAAE,GAAG;EACRC,IAAI,EAAE,UAAU;EAChBC,IAAI,EAAE,gBAAgB;EACtBC,WAAW,EAAE,6CAA6C;EAC1DC,YAAY,EAAE,YAAY;EAC1BC,aAAa,EAAE,EAAE;EACjBC,cAAc,EAAE,GAAG;EACnBC,iBAAiB,EAAE,GAAG;EACtBC,SAAS,EAAE,YAAY;EACvBC,OAAO,EAAE,YAAY;EACrBC,QAAQ,EAAE,IAAI;EACdC,UAAU,EAAE,EAAE;EACdC,SAAS,EAAE;AACb,CAAC,EACD;EACEZ,GAAG,EAAE,GAAG;EACRC,IAAI,EAAE,WAAW;EACjBC,IAAI,EAAE,gBAAgB;EACtBC,WAAW,EAAE,0CAA0C;EACvDC,YAAY,EAAE,cAAc;EAC5BC,aAAa,EAAE,EAAE;EACjBC,cAAc,EAAE,GAAG;EACnBC,iBAAiB,EAAE,EAAE;EACrBC,SAAS,EAAE,YAAY;EACvBC,OAAO,EAAE,YAAY;EACrBC,QAAQ,EAAE,IAAI;EACdC,UAAU,EAAE,GAAG;EACfC,SAAS,EAAE;AACb,CAAC,CACF;;AAED;AACA,MAAMC,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC;AACtB,MAAMC,sBAAsB,GAAGhB,cAAc,CAACiB,MAAM,CAACC,KAAK,IAAI;EAC5D,MAAMT,SAAS,GAAG,IAAIM,IAAI,CAACG,KAAK,CAACT,SAAS,CAAC;EAC3C,MAAMC,OAAO,GAAG,IAAIK,IAAI,CAACG,KAAK,CAACR,OAAO,CAAC;EAEvC,IAAII,GAAG,GAAGL,SAAS,EAAE;IACnB,OAAO,IAAI,CAAC,CAAC;EACf,CAAC,MAAM,IAAIK,GAAG,GAAGJ,OAAO,EAAE;IACxB,OAAO,KAAK,CAAC,CAAC;EAChB,CAAC,MAAM,IAAI,CAACQ,KAAK,CAACP,QAAQ,EAAE;IAC1B,OAAO,KAAK,CAAC,CAAC;EAChB,CAAC,MAAM,IAAIO,KAAK,CAACN,UAAU,IAAIM,KAAK,CAACL,SAAS,IAAIK,KAAK,CAACN,UAAU,EAAE;IAClE,OAAO,KAAK,CAAC,CAAC;EAChB,CAAC,MAAM;IACL,OAAO,IAAI,CAAC,CAAC;EACf;AACF,CAAC,CAAC;AAEF,MAAMO,YAAY,GAAG;EACnBC,UAAU,EAAEJ,sBAAsB;EAClCK,OAAO,EAAE,KAAK;EACdC,KAAK,EAAE;AACT,CAAC;AAED,MAAMC,gBAAgB,GAAGA,CAACC,KAAK,GAAGL,YAAY,EAAEM,MAAM,KAAK;EACzD,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAK3B,gBAAgB,CAAC4B,qBAAqB;MACzC,OAAO;QACL,GAAGH,KAAK;QACRH,OAAO,EAAE,IAAI;QACbC,KAAK,EAAE;MACT,CAAC;IAEH,KAAKvB,gBAAgB,CAAC6B,6BAA6B;MACjD,OAAO;QACL,GAAGJ,KAAK;QACRH,OAAO,EAAE,KAAK;QACdD,UAAU,EAAEK,MAAM,CAACI,OAAO;QAC1BP,KAAK,EAAE;MACT,CAAC;IAEH,KAAKvB,gBAAgB,CAAC+B,6BAA6B;MACjD,OAAO;QACL,GAAGN,KAAK;QACRH,OAAO,EAAE,KAAK;QACdC,KAAK,EAAEG,MAAM,CAACI;MAChB,CAAC;IAEH,KAAK9B,gBAAgB,CAACgC,qBAAqB;MACzC,OAAO;QACL,GAAGP,KAAK;QACRJ,UAAU,EAAEI,KAAK,CAACJ,UAAU,CAACY,GAAG,CAAEC,SAAS,IACzCA,SAAS,CAAChC,GAAG,KAAKwB,MAAM,CAACI,OAAO,CAAC5B,GAAG,GAChC;UAAE,GAAGgC,SAAS;UAAEC,MAAM,EAAE,IAAI;UAAEC,MAAM,EAAE,IAAIpB,IAAI,CAAC,CAAC,CAACqB,WAAW,CAAC;QAAE,CAAC,GAChEH,SACN;MACF,CAAC;IAEH;MACE,OAAOT,KAAK;EAChB;AACF,CAAC;AAED,eAAeD,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}