.form-control-plaintext {
  padding: 0.375rem 0;
}

.featured-image {
  width: 100%;
  height: 250px;
  object-fit: cover;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.thumbnail {
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 2px solid transparent;
}

.thumbnail:hover {
  transform: translateY(-2px);
}

.thumbnail.active {
  border-color: #0d6efd;
}

.amenity-icon {
  font-size: 1.2rem;
  color: #0d6efd;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.card {
  border-radius: 12px;
  overflow: hidden;
}

.form-label {
  color: #495057;
}

.form-control:focus {
  border-color: #86b7fe;
  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.btn-primary,
.btn-success {
  padding: 0.5rem 1rem;
  border-radius: 6px;
}

