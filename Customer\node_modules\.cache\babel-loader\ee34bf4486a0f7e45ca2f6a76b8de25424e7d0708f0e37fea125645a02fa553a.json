{"ast": null, "code": "var _jsxFileName = \"E:\\\\WDP301_UROOM\\\\Customer\\\\src\\\\pages\\\\customer\\\\information\\\\components\\\\MyPromotion.jsx\";\nimport React from \"react\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconsole.log(\"MyPromotion.jsx file loaded successfully\");\nconst MyPromotion = () => {\n  console.log(\"MyPromotion component is rendering...\");\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      children: \"My Promotions - Test\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 10,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      children: \"If you can see this, the component is working!\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 11,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 9,\n    columnNumber: 5\n  }, this);\n};\n_c = MyPromotion;\nexport default MyPromotion;\nvar _c;\n$RefreshReg$(_c, \"MyPromotion\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "console", "log", "MyPromotion", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["E:/WDP301_UROOM/Customer/src/pages/customer/information/components/MyPromotion.jsx"], "sourcesContent": ["import React from \"react\";\r\n\r\nconsole.log(\"MyPromotion.jsx file loaded successfully\");\r\n\r\nconst MyPromotion = () => {\r\n  console.log(\"MyPromotion component is rendering...\");\r\n  \r\n  return (\r\n    <div>\r\n      <h2>My Promotions - Test</h2>\r\n      <p>If you can see this, the component is working!</p>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default MyPromotion;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1BC,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;AAEvD,MAAMC,WAAW,GAAGA,CAAA,KAAM;EACxBF,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;EAEpD,oBACEF,OAAA;IAAAI,QAAA,gBACEJ,OAAA;MAAAI,QAAA,EAAI;IAAoB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAC7BR,OAAA;MAAAI,QAAA,EAAG;IAA8C;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAClD,CAAC;AAEV,CAAC;AAACC,EAAA,GATIN,WAAW;AAWjB,eAAeA,WAAW;AAAC,IAAAM,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}